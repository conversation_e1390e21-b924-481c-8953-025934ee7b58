import { BaseChatModel } from '@langchain/core/language_models/chat_models.js';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import {
  RunnableMap,
  RunnableParallel,
  RunnableSequence,
} from '@langchain/core/runnables';
import { z } from 'zod';

import { UnexpectedError } from '@awe/core';

import { type JobDraft, jobDraft, toDraft } from './job.draft.js';

export const extractJobStream = async function* (
  jobText: string,
  model: BaseChatModel
): AsyncGenerator<JobExtractionStreamEvent> {
  // 1. Identify sections
  const sectionIdentifier = createJobSectionIdentifier(model);
  const { sections } = await sectionIdentifier.invoke({ jobText });
  yield { type: 'sections_identified', sections };

  // 2. Set up parallel extraction
  const parallelExtraction: Record<string, RunnableSequence | RunnableMap> = {};

  for (const section of sections) {
    if (section.name === 'responsibilities') {
      parallelExtraction[section.name] =
        createResponsibilitiesSectionExtractor(model);
    } else if (section.name === 'qualifications') {
      parallelExtraction[section.name] =
        createQualificationsSectionExtractor(model);
    } else {
      parallelExtraction[section.name] = createSimpleJobSectionExtractor(
        section.name,
        model
      );
    }
  }
  const extractionPipeline = RunnableParallel.from(parallelExtraction);

  const sectionInputs = sections.reduce((acc, s) => {
    acc[s.name] = { content: s.content };
    return acc;
  }, {} as Record<JobSection['name'], Pick<JobSection, 'content'>>);

  // 3. Stream results and merge
  const fullJob: Partial<JobDraft> = {};
  const stream = await extractionPipeline.stream(sectionInputs);

  for await (const chunk of stream) {
    // The chunk is an object with a single key, e.g., { responsibilities: { ... } }
    const sectionName = Object.keys(chunk)[0];

    if (!sectionName) continue;

    const sectionData = chunk[sectionName] as Partial<JobDraft>;

    Object.assign(fullJob, sectionData);

    yield {
      type: 'extraction_result',
      section: sectionName,
      data: sectionData,
    };
  }

  yield { type: 'final_result', job: toDraft(fullJob) };
};

const simpleJobSections = z.object({
  name: z.enum([
    'basic_info',
    'company_info',
    'job_details',
    'compensation',
    'location',
    'skills',
    'experience',
  ]),
  content: z.string(),
});

const responsibilitiesSection = z.object({
  name: z.literal('responsibilities'),
  content: z.string(),
});

const qualificationsSection = z.object({
  name: z.literal('qualifications'),
  content: z.string(),
});

const jobSectionSchema = z.discriminatedUnion('name', [
  simpleJobSections,
  responsibilitiesSection,
  qualificationsSection,
]);

type SimpleJobSection = z.infer<typeof simpleJobSections>;
type ResponsibilitiesSection = z.infer<typeof responsibilitiesSection>;
type QualificationsSection = z.infer<typeof qualificationsSection>;
type JobSection =
  | SimpleJobSection
  | ResponsibilitiesSection
  | QualificationsSection;

const identifiedJobSectionsSchema = z.object({
  sections: z.array(jobSectionSchema),
});

const createJobSectionIdentifier = (model: BaseChatModel) => {
  const parser = StructuredOutputParser.fromZodSchema(
    identifiedJobSectionsSchema
  );

  const prompt = ChatPromptTemplate.fromTemplate(
    `You are an expert job posting parser. Your task is to identify and segment the following job posting text into logical sections.

Possible sections are: ${simpleJobSections.shape.name.options.join(', ')}, ${
      responsibilitiesSection.shape.name.value
    }, ${qualificationsSection.shape.name.value}.

Job Posting Text:
{jobText}

{format_instructions}

Make sure to identify distinct sections like responsibilities, qualifications, company info, etc.
Group related content together - don't split similar responsibilities into separate sections.

Respond ONLY with the structured JSON output.`
  );

  return RunnableSequence.from([
    {
      jobText: (input: { jobText: string }) => input.jobText,
      format_instructions: () => parser.getFormatInstructions(),
    },
    prompt,
    model,
    parser,
  ]);
};

const createResponsibilitiesListSplitter = (model: BaseChatModel) => {
  const splitterPrompt = ChatPromptTemplate.fromTemplate(
    `You are an expert job posting parser. Split the following responsibilities text into an array of individual responsibility items. 
Return ONLY a JSON array of strings, each string representing one responsibility (no explanations).

Responsibilities Text:
{responsibilities_content}`
  );
  const arrayParser = StructuredOutputParser.fromZodSchema(
    z.array(z.string()).transform((entries) =>
      entries.map((data, index) => {
        return `${index + 1}: ${data}`;
      })
    )
  );
  return RunnableSequence.from([
    {
      responsibilities_content: (
        input: Record<
          'responsibilities',
          Pick<ResponsibilitiesSection, 'content'>
        >
      ) => input.responsibilities.content,
    },
    splitterPrompt,
    model,
    arrayParser,
  ]);
};

const createResponsibilitiesSectionExtractor = (model: BaseChatModel) => {
  const entryPrompt = ChatPromptTemplate.fromTemplate(
    `You are an expert job posting parser. Extract all responsibility information from the provided text.

{responsibilities_entries}

---

{format_instructions}
Respond ONLY with the structured JSON output.`
  );
  const entryParser = StructuredOutputParser.fromZodSchema(
    jobDraft.pick({ responsibilities: true })
  );
  const extractor = RunnableSequence.from([
    {
      responsibilities_entries: (input: { responsibilities_entries: string }) =>
        input.responsibilities_entries,
      format_instructions: () => entryParser.getFormatInstructions(),
    },
    entryPrompt,
    model,
    entryParser,
  ]);

  // The main sequence: split with model, extract each, aggregate
  return RunnableSequence.from([
    // Step 1: split the responsibilities section into entries using a model call
    async (
      input: Record<
        'responsibilities',
        Pick<ResponsibilitiesSection, 'content'>
      >
    ) => {
      const splitter = createResponsibilitiesListSplitter(model);
      const entries = await splitter.invoke(input);

      return entries;
    },
    // Step 2: extract each entry
    async (entries: string[]) => {
      const result = await extractor.invoke({
        responsibilities_entries: entries.join('\n'),
      });

      return result;
    },
    // Step 3: aggregate into the expected output
    (entries) => entries,
  ]);
};

const createQualificationsListSplitter = (model: BaseChatModel) => {
  const splitterPrompt = ChatPromptTemplate.fromTemplate(
    `You are an expert job posting parser. Split the following qualifications text into an array of individual qualification items. 
Return ONLY a JSON array of strings, each string representing one qualification (no explanations).

Qualifications Text:
{qualifications_content}`
  );
  const arrayParser = StructuredOutputParser.fromZodSchema(
    z.array(z.string()).transform((entries) =>
      entries.map((data, index) => {
        return `${index + 1}: ${data}`;
      })
    )
  );
  return RunnableSequence.from([
    {
      qualifications_content: (
        input: Record<'qualifications', Pick<QualificationsSection, 'content'>>
      ) => input.qualifications.content,
    },
    splitterPrompt,
    model,
    arrayParser,
  ]);
};

const createQualificationsSectionExtractor = (model: BaseChatModel) => {
  const entryPrompt = ChatPromptTemplate.fromTemplate(
    `You are an expert job posting parser. Extract all qualification information from the provided text.

{qualifications_entries}

---

{format_instructions}
Respond ONLY with the structured JSON output.`
  );
  const entryParser = StructuredOutputParser.fromZodSchema(
    jobDraft.pick({ qualifications: true })
  );
  const extractor = RunnableSequence.from([
    {
      qualifications_entries: (input: { qualifications_entries: string }) =>
        input.qualifications_entries,
      format_instructions: () => entryParser.getFormatInstructions(),
    },
    entryPrompt,
    model,
    entryParser,
  ]);

  // The main sequence: split with model, extract each, aggregate
  return RunnableSequence.from([
    // Step 1: split the qualifications section into entries using a model call
    async (
      input: Record<'qualifications', Pick<QualificationsSection, 'content'>>
    ) => {
      const splitter = createQualificationsListSplitter(model);
      const entries = await splitter.invoke(input);

      return entries;
    },
    // Step 2: extract each entry
    async (entries: string[]) => {
      const result = await extractor.invoke({
        qualifications_entries: entries.join('\n'),
      });

      return result;
    },
    // Step 3: aggregate into the expected output
    (entries) => entries,
  ]);
};

const createSimpleJobSectionExtractor = (
  sectionName: SimpleJobSection['name'],
  model: BaseChatModel
) => {
  // Define a schema for each section to keep prompts focused
  const sectionSchemas = {
    basic_info: jobDraft.pick({
      title: true,
      company: true,
      type: true,
      date: true,
    }),
    company_info: jobDraft.pick({
      company: true,
      company_meta: true,
    }),
    job_details: jobDraft.pick({
      description: true,
      type: true,
    }),
    compensation: jobDraft.pick({
      salary: true,
    }),
    location: jobDraft.pick({
      location: true,
      remote: true,
    }),
    skills: jobDraft.pick({
      skills: true,
    }),
    experience: jobDraft.pick({
      experience: true,
    }),
    other: z.object({}), // No specific extraction for 'other'
  };

  const schema = sectionSchemas[sectionName];

  if (!schema) {
    throw new UnexpectedError(
      `No schema found for job section: ${sectionName}`
    );
  }

  const parser = StructuredOutputParser.fromZodSchema(schema);

  const prompt = ChatPromptTemplate.fromTemplate(
    `You are an expert job posting parser. Extract information for the "{section}" section based on the text provided.

Section Text:
{section_content}

{format_instructions}

Respond ONLY with the structured JSON output.`
  );

  return RunnableSequence.from([
    {
      section: () => sectionName,
      section_content: (
        input: Record<JobSection['name'], Pick<SimpleJobSection, 'content'>>
      ) => {
        return input[sectionName].content;
      },
      format_instructions: () => parser.getFormatInstructions(),
    },
    prompt,
    model,
    parser,
  ]);
};

export type JobExtractionStreamEvent =
  | { type: 'sections_identified'; sections: JobSection[] }
  | { type: 'extraction_result'; section: string; data: Partial<JobDraft> }
  | { type: 'final_result'; job: JobDraft };
